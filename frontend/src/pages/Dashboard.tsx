/**
 * Main Dashboard page
 */

import React, { useEffect } from 'react';
import { 
  FileText, 
  Settings, 
  Upload, 
  Play,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { usePreferencesStore } from '../store/preferencesStore';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { PreferencesPanel } from '../components/dashboard/PreferencesPanel';
import { StatsCards } from '../components/dashboard/StatsCards';

export const Dashboard: React.FC = () => {
  const { loadPreferences, loadAnalysisTypes } = usePreferencesStore();

  useEffect(() => {
    // Load initial data
    loadPreferences();
    loadAnalysisTypes();
  }, [loadPreferences, loadAnalysisTypes]);

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Configure your preferences and upload documents for analysis
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <StatsCards />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Preferences Panel - Takes 2 columns on large screens */}
        <div className="lg:col-span-2">
          <Card>
            <Card.Header>
              <div className="flex items-center">
                <Settings className="w-5 h-5 text-primary-400 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">
                  Analysis Preferences
                </h2>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                Configure your analysis settings and document processing preferences
              </p>
            </Card.Header>
            <Card.Body>
              <PreferencesPanel />
            </Card.Body>
          </Card>
        </div>

        {/* Workflow Panel - Takes 1 column */}
        <div className="space-y-6">
          {/* Document Upload */}
          <Card>
            <Card.Header>
              <div className="flex items-center">
                <Upload className="w-5 h-5 text-primary-400 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Upload Documents
                </h3>
              </div>
            </Card.Header>
            <Card.Body>
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FileText className="w-8 h-8 text-gray-400" />
                </div>
                <p className="text-gray-600 mb-4">
                  No documents uploaded yet
                </p>
                <Button variant="primary" size="sm">
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Files
                </Button>
              </div>
            </Card.Body>
          </Card>

          {/* Generate Button */}
          <Card>
            <Card.Header>
              <div className="flex items-center">
                <Play className="w-5 h-5 text-success-500 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Generate Analysis
                </h3>
              </div>
            </Card.Header>
            <Card.Body>
              <div className="text-center py-4">
                <p className="text-gray-600 mb-4 text-sm">
                  Upload documents and configure preferences to enable generation
                </p>
                <Button 
                  variant="success" 
                  size="lg" 
                  className="w-full"
                  disabled={true}
                >
                  <Play className="w-4 h-4 mr-2" />
                  Generate
                </Button>
                <p className="text-xs text-gray-500 mt-2">
                  Complete setup to enable this feature
                </p>
              </div>
            </Card.Body>
          </Card>

          {/* Quick Actions */}
          <Card>
            <Card.Header>
              <h3 className="text-lg font-semibold text-gray-900">
                Quick Actions
              </h3>
            </Card.Header>
            <Card.Body>
              <div className="space-y-3">
                <Button variant="secondary" size="sm" className="w-full justify-start">
                  <Clock className="w-4 h-4 mr-2" />
                  View Recent Sessions
                </Button>
                <Button variant="secondary" size="sm" className="w-full justify-start">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Analysis History
                </Button>
                <Button variant="secondary" size="sm" className="w-full justify-start">
                  <AlertCircle className="w-4 h-4 mr-2" />
                  Help & Support
                </Button>
              </div>
            </Card.Body>
          </Card>
        </div>
      </div>
    </div>
  );
};
