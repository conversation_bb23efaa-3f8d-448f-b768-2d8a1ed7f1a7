/**
 * Preferences related types for CaseBuilder AI
 */

export enum AnalysisType {
  POLICE_REPORT_SUMMARY = "police_report_summary",
  FACTS_LIABILITY = "facts_liability",
  IN_DEPTH_LIABILITY = "in_depth_liability",
  MEDICAL_ANALYSIS = "medical_analysis",
  MEDICAL_EXPENSES = "medical_expenses",
  FUTURE_TREATMENT = "future_treatment",
  ANALYZE_INJURIES = "analyze_injuries",
  ACCIDENT_SCENE = "accident_scene",
  PROPERTY_DAMAGE = "property_damage",
  GENERATE_DEMAND_LETTER = "generate_demand_letter"
}

export enum DetailLevel {
  BASIC = "basic",
  STANDARD = "standard",
  COMPREHENSIVE = "comprehensive"
}

export enum ContentEmphasis {
  FACTS = "facts",
  LEGAL = "legal",
  MEDICAL = "medical",
  FINANCIAL = "financial"
}

export enum AnalysisStyle {
  NARRATIVE = "narrative",
  CHRONOLOGICAL = "chronological",
  BULLETED = "bulleted",
  TABULAR = "tabular"
}

export enum DemandLetterSection {
  INTRODUCTION = "introduction",
  FACTS = "facts",
  LIABILITY = "liability",
  DAMAGES = "damages",
  MEDICAL_EXPENSES = "medical_expenses",
  FUTURE_TREATMENT = "future_treatment",
  GENERAL_DAMAGES = "general_damages",
  CONCLUSION = "conclusion"
}

export enum LetterLength {
  CONCISE = "concise",
  STANDARD = "standard",
  DETAILED = "detailed"
}

export enum ToneSetting {
  PROFESSIONAL = "professional",
  ASSERTIVE = "assertive",
  DIPLOMATIC = "diplomatic"
}

export enum DemandLetterEmphasis {
  LEGAL = "legal",
  MEDICAL = "medical",
  FINANCIAL = "financial",
  EMOTIONAL = "emotional"
}

export interface AnalysisPreferences {
  selected_analyses: AnalysisType[];
  detail_level: DetailLevel;
  content_emphasis: ContentEmphasis;
  analysis_style: AnalysisStyle;
}

export interface DemandLetterPreferences {
  sections: DemandLetterSection[];
  length: LetterLength;
  tone: ToneSetting;
  content_emphasis: DemandLetterEmphasis;
}

export interface UserPreferences {
  analysis: AnalysisPreferences;
  demand_letter: DemandLetterPreferences;
}

export interface PreferencesUpdateRequest {
  analysis?: Partial<AnalysisPreferences>;
  demand_letter?: Partial<DemandLetterPreferences>;
}

export interface PreferencesResponse {
  success: boolean;
  preferences: UserPreferences;
  message: string;
  timestamp: string;
}

export interface AnalysisTypeInfo {
  name: string;
  description: string;
  category: string;
}

export interface AnalysisTypesResponse {
  analysis_types: Record<AnalysisType, AnalysisTypeInfo>;
  categories: Record<string, string>;
}

// Default preferences
export const DEFAULT_ANALYSIS_PREFERENCES: AnalysisPreferences = {
  selected_analyses: [],
  detail_level: DetailLevel.STANDARD,
  content_emphasis: ContentEmphasis.FACTS,
  analysis_style: AnalysisStyle.NARRATIVE
};

export const DEFAULT_DEMAND_LETTER_PREFERENCES: DemandLetterPreferences = {
  sections: [
    DemandLetterSection.INTRODUCTION,
    DemandLetterSection.FACTS,
    DemandLetterSection.LIABILITY,
    DemandLetterSection.DAMAGES,
    DemandLetterSection.CONCLUSION
  ],
  length: LetterLength.STANDARD,
  tone: ToneSetting.PROFESSIONAL,
  content_emphasis: DemandLetterEmphasis.LEGAL
};

export const DEFAULT_USER_PREFERENCES: UserPreferences = {
  analysis: DEFAULT_ANALYSIS_PREFERENCES,
  demand_letter: DEFAULT_DEMAND_LETTER_PREFERENCES
};
