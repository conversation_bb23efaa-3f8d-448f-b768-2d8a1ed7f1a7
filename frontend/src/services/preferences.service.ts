/**
 * Preferences service for CaseBuilder AI
 */

import { apiClient } from './api';
import { 
  PreferencesResponse, 
  PreferencesUpdateRequest,
  AnalysisTypesResponse
} from '../types/preferences.types';

export const preferencesService = {
  /**
   * Get current user preferences
   */
  getPreferences: async (): Promise<PreferencesResponse> => {
    const response = await apiClient.get<PreferencesResponse>('/preferences/');
    return response.data;
  },

  /**
   * Update user preferences
   */
  updatePreferences: async (update: PreferencesUpdateRequest): Promise<PreferencesResponse> => {
    const response = await apiClient.put<PreferencesResponse>('/preferences/', update);
    return response.data;
  },

  /**
   * Reset preferences to defaults
   */
  resetPreferences: async (): Promise<PreferencesResponse> => {
    const response = await apiClient.post<PreferencesResponse>('/preferences/reset');
    return response.data;
  },

  /**
   * Get available analysis types and their descriptions
   */
  getAnalysisTypes: async (): Promise<AnalysisTypesResponse> => {
    const response = await apiClient.get<AnalysisTypesResponse>('/preferences/analysis-types');
    return response.data;
  },

  /**
   * Get default preferences
   */
  getDefaultPreferences: async (): Promise<PreferencesResponse> => {
    const response = await apiClient.get<PreferencesResponse>('/preferences/defaults');
    return response.data;
  }
};
