/**
 * API service for CaseBuilder AI
 * Handles all API calls to the backend
 */

import axios, { AxiosResponse } from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Types
export interface DocumentUploadResponse {
  document_id: string;
  filename: string;
  content_type: string;
  file_size: number;
  processing_status: string;
  message: string;
}

export interface DocumentListResponse {
  documents: Array<{
    id: string;
    filename: string;
    content_type: string;
    file_size: number;
    processing_status: string;
    document_type?: string;
    uploaded_at: number;
  }>;
  total: number;
}

export interface AnalysisRequest {
  document_ids: string[];
  preferences: any;
  selected_analyses: string[];
}

export interface AnalysisResponse {
  analysis_id: string;
  status: string;
  progress: number;
  result?: string;
  started_at: number;
  completed_at?: number;
  message: string;
}

export interface ProgressResponse {
  task_id: string;
  status: string;
  progress: number;
  message: string;
  started_at: number;
  updated_at: number;
}

// Document API
export const documentAPI = {
  // Upload a document
  upload: async (file: File): Promise<DocumentUploadResponse> => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response: AxiosResponse<DocumentUploadResponse> = await apiClient.post(
      '/api/documents/upload',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    
    return response.data;
  },

  // List documents
  list: async (): Promise<DocumentListResponse> => {
    const response: AxiosResponse<DocumentListResponse> = await apiClient.get(
      '/api/documents/'
    );
    return response.data;
  },

  // Get document details
  get: async (documentId: string): Promise<any> => {
    const response = await apiClient.get(`/api/documents/${documentId}`);
    return response.data;
  },

  // Delete document
  delete: async (documentId: string): Promise<void> => {
    await apiClient.delete(`/api/documents/${documentId}`);
  },
};

// Analysis API
export const analysisAPI = {
  // Start analysis
  start: async (request: AnalysisRequest): Promise<AnalysisResponse> => {
    const response: AxiosResponse<AnalysisResponse> = await apiClient.post(
      '/api/analysis/start',
      request
    );
    return response.data;
  },

  // List analyses
  list: async (): Promise<any> => {
    const response = await apiClient.get('/api/analysis/');
    return response.data;
  },

  // Get analysis results
  get: async (analysisId: string): Promise<AnalysisResponse> => {
    const response: AxiosResponse<AnalysisResponse> = await apiClient.get(
      `/api/analysis/${analysisId}`
    );
    return response.data;
  },

  // Get analysis progress
  getProgress: async (analysisId: string): Promise<ProgressResponse> => {
    const response: AxiosResponse<ProgressResponse> = await apiClient.get(
      `/api/analysis/${analysisId}/progress`
    );
    return response.data;
  },
};

// Preferences API
export const preferencesAPI = {
  // Get user preferences
  get: async (): Promise<any> => {
    const response = await apiClient.get('/api/preferences/');
    return response.data;
  },

  // Update user preferences
  update: async (preferences: any): Promise<any> => {
    const response = await apiClient.put('/api/preferences/', preferences);
    return response.data;
  },

  // Get analysis types
  getAnalysisTypes: async (): Promise<any> => {
    const response = await apiClient.get('/api/preferences/analysis-types');
    return response.data;
  },
};

// Auth API
export const authAPI = {
  // Login
  login: async (username: string, password: string): Promise<any> => {
    const response = await apiClient.post('/api/auth/login', {
      username,
      password,
    });
    return response.data;
  },

  // Logout
  logout: async (): Promise<void> => {
    await apiClient.post('/api/auth/logout');
  },

  // Get current user
  getCurrentUser: async (): Promise<any> => {
    const response = await apiClient.get('/api/auth/me');
    return response.data;
  },
};

// Utility functions
export const handleApiError = (error: any): string => {
  if (error.response?.data?.detail) {
    return error.response.data.detail;
  }
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
};

export default {
  documentAPI,
  analysisAPI,
  preferencesAPI,
  authAPI,
  handleApiError,
};
