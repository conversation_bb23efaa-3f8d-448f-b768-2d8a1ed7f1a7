/**
 * Authentication service for CaseBuilder AI
 */

import { apiClient } from './api';
import { LoginRequest, LoginResponse, User } from '../types/auth.types';

export const authService = {
  /**
   * Login user with credentials
   */
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    // Mock login for development - accept any credentials
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (credentials.username && credentials.password) {
          resolve({
            success: true,
            access_token: 'mock-jwt-token-' + Date.now(),
            token_type: 'bearer',
            expires_in: 1800, // 30 minutes
            user: {
              username: credentials.username,
              first_name: 'Demo',
              last_name: 'User',
              email: '<EMAIL>',
              user_id: 1,
              session_id: 'mock-session-' + Date.now()
            },
            message: 'Login successful',
            timestamp: new Date().toISOString()
          });
        } else {
          reject({
            response: {
              data: {
                detail: 'Invalid username or password'
              }
            }
          });
        }
      }, 1000); // Simulate network delay
    });
  },

  /**
   * Logout current user
   */
  logout: async (): Promise<void> => {
    // Mock logout
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 500);
    });
  },

  /**
   * Get current user information
   */
  getCurrentUser: async (): Promise<{ user: User; message: string }> => {
    const response = await apiClient.get('/auth/me');
    return response.data;
  },

  /**
   * Refresh authentication token
   */
  refreshToken: async (): Promise<LoginResponse> => {
    const response = await apiClient.post<LoginResponse>('/auth/refresh');
    return response.data;
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated: (): boolean => {
    try {
      const authData = localStorage.getItem('casebuilder-auth');
      if (!authData) return false;

      const { state } = JSON.parse(authData);
      return !!(state?.token && state?.isAuthenticated);
    } catch {
      return false;
    }
  },

  /**
   * Get stored token
   */
  getToken: (): string | null => {
    try {
      const authData = localStorage.getItem('casebuilder-auth');
      if (!authData) return null;

      const { state } = JSON.parse(authData);
      return state?.token || null;
    } catch {
      return null;
    }
  },

  /**
   * Clear authentication data
   */
  clearAuth: (): void => {
    localStorage.removeItem('casebuilder-auth');
  }
};
