/**
 * Authentication service for CaseBuilder AI
 */

import { apiClient } from './api';
import { LoginRequest, LoginResponse, User } from '../types/auth.types';

export const authService = {
  /**
   * Login user with credentials
   */
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response = await apiClient.post<LoginResponse>('/auth/login', credentials);
    return response.data;
  },

  /**
   * Logout current user
   */
  logout: async (): Promise<void> => {
    await apiClient.post('/auth/logout');
  },

  /**
   * Get current user information
   */
  getCurrentUser: async (): Promise<{ user: User; message: string }> => {
    const response = await apiClient.get('/auth/me');
    return response.data;
  },

  /**
   * Refresh authentication token
   */
  refreshToken: async (): Promise<LoginResponse> => {
    const response = await apiClient.post<LoginResponse>('/auth/refresh');
    return response.data;
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated: (): boolean => {
    try {
      const authData = localStorage.getItem('casebuilder-auth');
      if (!authData) return false;

      const { state } = JSON.parse(authData);
      return !!(state?.token && state?.isAuthenticated);
    } catch {
      return false;
    }
  },

  /**
   * Get stored token
   */
  getToken: (): string | null => {
    try {
      const authData = localStorage.getItem('casebuilder-auth');
      if (!authData) return null;

      const { state } = JSON.parse(authData);
      return state?.token || null;
    } catch {
      return null;
    }
  },

  /**
   * Clear authentication data
   */
  clearAuth: (): void => {
    localStorage.removeItem('casebuilder-auth');
  }
};
