const set = require('regenerate')(0xB9C, 0xBD0, 0xBD7, 0x1CDA, 0xA8F3, 0x11301, 0x11303, 0x11FFF);
set.addRange(0x951, 0x952).addRange(0x964, 0x965).addRange(0xB82, 0xB83).addRange(0xB85, 0xB8A).addRange(0xB8E, 0xB90).addRange(0xB92, 0xB95).addRange(0xB99, 0xB9A).addRange(0xB9E, 0xB9F).addRange(0xBA3, 0xBA4).addRange(0xBA8, 0xBAA).addRange(0xBAE, 0xBB9).addRange(0xBBE, 0xBC2).addRange(0xBC6, 0xBC8).addRange(0xBCA, 0xBCD).addRange(0xBE6, 0xBFA).addRange(0x1133B, 0x1133C).addRange(0x11FC0, 0x11FF1);
exports.characters = set;
