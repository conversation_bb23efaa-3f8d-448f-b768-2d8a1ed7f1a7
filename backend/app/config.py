"""
Configuration settings for CaseBuilder AI FastAPI application.
"""

from pydantic import BaseSettings, Field
from typing import List, Optional
import os
from functools import lru_cache


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application settings
    app_name: str = "CaseBuilder AI"
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=True, env="DEBUG")
    
    # Security settings
    secret_key: str = Field(..., env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # CORS settings
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        env="ALLOWED_ORIGINS"
    )
    allowed_hosts: List[str] = Field(
        default=["localhost", "127.0.0.1"],
        env="ALLOWED_HOSTS"
    )
    
    # Database settings (MySQL for user authentication only)
    database_host: str = Field(..., env="DATABASE_HOST")
    database_port: int = Field(default=3306, env="DATABASE_PORT")
    database_user: str = Field(..., env="DATABASE_USER")
    database_password: str = Field(..., env="DATABASE_PASSWORD")
    database_name: str = Field(..., env="DATABASE_NAME")
    
    # Cloud SQL settings (for production)
    instance_connection_name: Optional[str] = Field(default=None, env="INSTANCE_CONNECTION_NAME")
    db_socket_dir: str = Field(default="/cloudsql", env="DB_SOCKET_DIR")
    
    # OpenAI settings
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    
    # Redis settings (for session management)
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    session_expire_seconds: int = Field(default=3600, env="SESSION_EXPIRE_SECONDS")  # 1 hour
    
    # File upload settings
    max_file_size: int = Field(default=50 * 1024 * 1024, env="MAX_FILE_SIZE")  # 50MB
    allowed_file_types: List[str] = Field(
        default=["pdf", "png", "jpg", "jpeg", "docx"],
        env="ALLOWED_FILE_TYPES"
    )
    
    # Processing settings
    max_concurrent_analyses: int = Field(default=5, env="MAX_CONCURRENT_ANALYSES")
    analysis_timeout_seconds: int = Field(default=300, env="ANALYSIS_TIMEOUT_SECONDS")  # 5 minutes
    
    class Config:
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()


# Development settings override
if os.getenv("ENVIRONMENT") == "development":
    class DevSettings(Settings):
        debug: bool = True
        allowed_origins: List[str] = ["*"]  # Allow all origins in development
        
    @lru_cache()
    def get_settings() -> DevSettings:
        return DevSettings()
