"""
Session middleware for managing volatile user sessions.
"""

from fastapi import Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware
import uuid
import time
import logging
from typing import Callable, Dict, Any, Optional

logger = logging.getLogger(__name__)


class SessionStore:
    """In-memory session store for volatile sessions."""
    
    def __init__(self):
        self.sessions: Dict[str, Dict[str, Any]] = {}
        self.session_timestamps: Dict[str, float] = {}
        
    def create_session(self, user_id: str) -> str:
        """Create a new session for a user."""
        session_id = str(uuid.uuid4())
        current_time = time.time()
        
        self.sessions[session_id] = {
            "user_id": user_id,
            "created_at": current_time,
            "last_accessed": current_time,
            "documents": {},
            "analyses": {},
            "preferences": {},
            "stats": {
                "documents_uploaded": 0,
                "analyses_completed": 0,
                "generations_completed": 0,
                "tokens_used": 0
            }
        }
        self.session_timestamps[session_id] = current_time
        
        logger.info(f"Created session {session_id} for user {user_id}")
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data by session ID."""
        if session_id in self.sessions:
            # Update last accessed time
            self.sessions[session_id]["last_accessed"] = time.time()
            self.session_timestamps[session_id] = time.time()
            return self.sessions[session_id]
        return None
    
    def update_session(self, session_id: str, data: Dict[str, Any]) -> bool:
        """Update session data."""
        if session_id in self.sessions:
            self.sessions[session_id].update(data)
            self.sessions[session_id]["last_accessed"] = time.time()
            self.session_timestamps[session_id] = time.time()
            return True
        return False
    
    def delete_session(self, session_id: str) -> bool:
        """Delete a session."""
        if session_id in self.sessions:
            user_id = self.sessions[session_id].get("user_id", "unknown")
            del self.sessions[session_id]
            del self.session_timestamps[session_id]
            logger.info(f"Deleted session {session_id} for user {user_id}")
            return True
        return False
    
    def cleanup_expired_sessions(self, max_age_seconds: int = 3600):
        """Clean up expired sessions."""
        current_time = time.time()
        expired_sessions = []
        
        for session_id, timestamp in self.session_timestamps.items():
            if current_time - timestamp > max_age_seconds:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.delete_session(session_id)
        
        if expired_sessions:
            logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
    
    def get_session_stats(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session statistics."""
        session = self.get_session(session_id)
        if session:
            current_time = time.time()
            duration = current_time - session["created_at"]
            
            return {
                "session_id": session_id,
                "user_id": session["user_id"],
                "duration": int(duration),
                "documents_uploaded": session["stats"]["documents_uploaded"],
                "analyses_completed": session["stats"]["analyses_completed"],
                "generations_completed": session["stats"]["generations_completed"],
                "tokens_used": session["stats"]["tokens_used"],
                "created_at": session["created_at"],
                "last_accessed": session["last_accessed"]
            }
        return None


# Global session store instance
session_store = SessionStore()


class SessionMiddleware(BaseHTTPMiddleware):
    """Middleware for managing user sessions."""
    
    def __init__(self, app, max_session_age: int = 3600):
        super().__init__(app)
        self.max_session_age = max_session_age
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and manage session."""
        
        # Clean up expired sessions periodically
        session_store.cleanup_expired_sessions(self.max_session_age)
        
        # Get session ID from headers or cookies
        session_id = request.headers.get("X-Session-ID") or request.cookies.get("session_id")
        
        # Attach session to request state
        if session_id:
            session_data = session_store.get_session(session_id)
            if session_data:
                request.state.session_id = session_id
                request.state.session_data = session_data
                request.state.user_id = session_data.get("user_id")
            else:
                # Invalid session ID
                request.state.session_id = None
                request.state.session_data = None
                request.state.user_id = None
        else:
            request.state.session_id = None
            request.state.session_data = None
            request.state.user_id = None
        
        # Process the request
        response = await call_next(request)
        
        return response
